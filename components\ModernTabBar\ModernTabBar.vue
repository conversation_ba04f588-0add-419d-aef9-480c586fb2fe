<template>
  <view class="modern-tabbar">
    <view class="tabbar-container">
      <view
        v-for="(item, index) in tabList"
        :key="index"
        class="tab-item"
        :class="{ active: currentIndex === index }"
        @click="switchTab(index)"
      >
        <view class="tab-icon">
          <text class="icon-text">{{ item.icon }}</text>
        </view>
        <text class="tab-text">{{ item.text }}</text>
        <view v-if="currentIndex === index" class="active-indicator"></view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  current: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['change'])

const currentIndex = ref(props.current)

const tabList = [
  {
    pagePath: 'pages/index/index',
    text: '首页',
    icon: '🏠'
  },
  {
    pagePath: 'pages/society/society',
    text: '社区',
    icon: '👥'
  },
  {
    pagePath: 'pages/enter/enter',
    text: '我的',
    icon: '👤'
  }
]

const switchTab = (index) => {
  if (currentIndex.value === index) return

  currentIndex.value = index
  emit('change', index)

  // 跳转页面
  uni.switchTab({
    url: `/${tabList[index].pagePath}`
  })
}

onMounted(() => {
  // 获取当前页面路径，设置对应的tab索引
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const route = currentPage.route

  const tabIndex = tabList.findIndex(item => item.pagePath === route)
  if (tabIndex !== -1) {
    currentIndex.value = tabIndex
  }
})
</script>

<style lang="scss" scoped>
.modern-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.active {
    .tab-icon {
      transform: translateY(-4rpx);
    }

    .tab-text {
      color: #667eea;
      font-weight: 600;
    }
  }
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-text {
    font-size: 40rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.tab-text {
  font-size: 20rpx;
  color: #8a8a8a;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1;
}

.active-indicator {
  position: absolute;
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 32rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3rpx;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 32rpx;
    opacity: 1;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .tabbar-container {
    height: 90rpx;
  }

  .tab-icon {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 6rpx;

    .icon-text {
      font-size: 32rpx;
    }
  }

  .tab-text {
    font-size: 18rpx;
  }
}
</style>
