<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">个人中心</text>
      <text class="header-subtitle">管理您的学习生活</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 未登录状态 -->
      <view v-if="!isLoggedIn" class="login-section">
        <view class="modern-card text-center mb-4">
          <view class="login-icon">
            <text class="icon-text">👋</text>
          </view>
          <text class="login-title">欢迎使用自律助手</text>
          <text class="login-desc">登录后享受更多个性化功能</text>
        </view>

        <view class="modern-grid">
          <navigator url="/pages/login/login" class="feature-card-nav">
            <view class="feature-card profile-card fade-in-up">
              <view class="card-icon">
                <text class="icon-text">🔑</text>
              </view>
              <view class="card-content">
                <text class="card-title">登录注册</text>
                <text class="card-desc">开启学习之旅</text>
              </view>
              <view class="card-arrow">
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </navigator>

          <navigator url="/pages/setting/setting" class="feature-card-nav">
            <view class="feature-card monitor-card fade-in-up">
              <view class="card-icon">
                <text class="icon-text">⚙️</text>
              </view>
              <view class="card-content">
                <text class="card-title">应用设置</text>
                <text class="card-desc">个性化配置</text>
              </view>
              <view class="card-arrow">
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </navigator>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-if="isLoggedIn" class="user-section">
        <!-- 用户信息卡片 -->
        <view class="modern-card user-profile-card mb-4">
          <view class="user-avatar-section">
            <image class="user-avatar" :src="userData.avatar" mode="aspectFill" />
            <view class="user-info">
              <view class="username-row">
                <text class="user-flag">{{ getFlag(userData.country) }}</text>
                <text class="username">{{ userData.username }}</text>
              </view>
              <text class="user-status">学习达人</text>
            </view>
          </view>
        </view>

        <!-- 功能网格 -->
        <view class="modern-grid three-column">
          <navigator url="/pages/user/user" class="feature-card-nav">
            <view class="feature-card profile-card fade-in-up">
              <view class="card-icon">
                <text class="icon-text">👤</text>
              </view>
              <view class="card-content">
                <text class="card-title">个人资料</text>
                <text class="card-desc">查看详细信息</text>
              </view>
              <view class="card-arrow">
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </navigator>

          <navigator url="/pages/setting/setting" class="feature-card-nav">
            <view class="feature-card monitor-card fade-in-up">
              <view class="card-icon">
                <text class="icon-text">⚙️</text>
              </view>
              <view class="card-content">
                <text class="card-title">应用设置</text>
                <text class="card-desc">个性化配置</text>
              </view>
              <view class="card-arrow">
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </navigator>

          <view class="feature-card-nav" @click="handleLogout">
            <view class="feature-card achievement-card fade-in-up">
              <view class="card-icon">
                <text class="icon-text">🚪</text>
              </view>
              <view class="card-content">
                <text class="card-title">退出登录</text>
                <text class="card-desc">安全退出账户</text>
              </view>
              <view class="card-arrow">
                <text class="arrow-icon">→</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>

    <!-- 自定义TabBar -->
    <ModernTabBar :current="2" />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import ModernTabBar from '@/components/ModernTabBar/ModernTabBar.vue';

// 用户数据和登录状态
const userData = ref({
  avatar: '/common/images/1.png',
  username: '新用户',
  country: '中国🇨🇳'
});
const isLoggedIn = ref(false);

// 页面加载时检查登录状态和用户数据
onMounted(() => {
  const storedLoginData = uni.getStorageSync('loginData');
  if (storedLoginData && storedLoginData.isLoggedIn) {
    isLoggedIn.value = true;
  }
  const storedUserData = uni.getStorageSync('userData');
  if (storedUserData) {
    userData.value = storedUserData;
  }
});

// 提取国旗
const getFlag = (country) => {
  if (!country) return '';
  const match = country.match(/�.*$/);
  return match ? match[0] : '';
};

// 跳转到登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  });
};

// 跳转到个人中心页面
const goToUserCenter = () => {
  uni.navigateTo({
    url: '/pages/user/user'
  });
};

// 跳转到设置页面
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/setting/setting'
  });
};

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.setStorageSync('loginData', { isLoggedIn: false });
        isLoggedIn.value = false;
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        });
      }
    }
  });
};
</script>

<style lang="scss">
// 使用全局现代化主题样式
.modern-page {
  padding-bottom: 120rpx; // 为自定义TabBar预留空间
}

.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 登录区域样式
.login-section {
  .login-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 24rpx;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 60rpx;
      color: white;
    }
  }

  .login-title {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 12rpx;
  }

  .login-desc {
    display: block;
    font-size: 24rpx;
    color: var(--gray-500);
    line-height: 1.4;
  }
}

// 用户信息卡片样式
.user-profile-card {
  .user-avatar-section {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }

  .user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: 4rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .user-info {
    flex: 1;
  }

  .username-row {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .user-flag {
    font-size: 32rpx;
    margin-right: 12rpx;
  }

  .username {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--gray-800);
  }

  .user-status {
    font-size: 24rpx;
    color: var(--gray-500);
    padding: 8rpx 16rpx;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 20rpx;
    display: inline-block;
  }
}
</style>