<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">学习社区</text>
      <text class="header-subtitle">与志同道合的伙伴一起成长</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <view class="modern-grid three-column">
        <!-- 排行榜卡片 -->
        <navigator url="/pages/rankinglist/rankinglist" class="feature-card-nav">
          <view class="feature-card achievement-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">🏆</text>
            </view>
            <view class="card-content">
              <text class="card-title">排行榜</text>
              <text class="card-desc">查看学习排名</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 好友卡片 -->
        <navigator url="/pages/friends/friends" class="feature-card-nav">
          <view class="feature-card social-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">👥</text>
            </view>
            <view class="card-content">
              <text class="card-title">学习好友</text>
              <text class="card-desc">添加学习伙伴</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 社交动态卡片 -->
        <navigator url="/pages/social/social" class="feature-card-nav">
          <view class="feature-card checkin-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">💬</text>
            </view>
            <view class="card-content">
              <text class="card-title">学习动态</text>
              <text class="card-desc">分享学习心得</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>

    <!-- 自定义TabBar -->
    <ModernTabBar :current="1" />
  </view>
</template>

<script setup>
import ModernTabBar from '@/components/ModernTabBar/ModernTabBar.vue'
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
// 使用全局现代化主题样式
.modern-page {
  padding-bottom: 120rpx; // 为自定义TabBar预留空间
}

.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}
</style>